import { <PERSON>, <PERSON>, Card, Statistic, Skeleton, Button, Progress } from "antd";
import {
    DollarSign,
    Users,
    BarChart2,
    Activity,
    TrendingUp,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    FilterX,
} from "lucide-react";

// Components
import StatCard from "@/features/crm/components/atoms/stat-card";
import PieChartCard from "@/features/crm/components/molecules/pie-chart-card";
import BarChartCard from "@/features/crm/components/molecules/bar-chart-card";
import LineChartCard from "@/features/crm/components/molecules/charts/line-chart-card";
import FunnelChartCard from "@/features/crm/components/molecules/funnel-chart-card";
import OrderFilterCard from "@/features/crm/components/molecules/order-filter-card";
import TopProductsCard from "@/features/crm/components/molecules/top-products-card";
import SalesPerformanceCard from "@/features/crm/components/molecules/sales-performance-card";
import LeadSourcesAnalyticsCard from "@/features/crm/components/molecules/lead-sources-analytics-card";
import OrdersTable from "@/features/crm/components/organisms/orders-table";

// Hooks and types
import { useNavigate, useSearchParams } from "react-router-dom";
import {
    useDashboardOrdersSummary,
    useDashboardOrdersAgents,
    useDashboardOrdersAnalytics,
    useDashboardOrdersHistorical,
    useDashboardOrdersProducts,
    useDashboardOrdersRecentOrders,
    createDashboardOrdersQueryParams,
    useDashboardOrdersLeadSources,
} from "@/features/crm/hooks/use-dashboard-orders";
import dayjs, { type Dayjs } from "dayjs";
import "dayjs/locale/es";
import type { DashboardOrderTendency } from "@/features/crm/types/dashboard/orders";

dayjs.locale("es");

export default function SalesDashboardTab() {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();

    const defaultDateRange: [Dayjs, Dayjs] = [
        dayjs().startOf("week"),
        dayjs().endOf("week"),
    ];

    // Crear parámetros de query reutilizables
    const queryParams = createDashboardOrdersQueryParams(
        searchParams,
        defaultDateRange,
    );

    // Hooks independientes para cada endpoint (carga progresiva)
    const { data: summaryData, isLoading: summaryLoading } =
        useDashboardOrdersSummary(queryParams);
    const { data: agentsData, isLoading: agentsLoading } =
        useDashboardOrdersAgents(queryParams);
    const { data: analyticsData, isLoading: analyticsLoading } =
        useDashboardOrdersAnalytics(queryParams);
    const { data: historicalData, isLoading: historicalLoading } =
        useDashboardOrdersHistorical(queryParams);
    const { data: productsData, isLoading: productsLoading } =
        useDashboardOrdersProducts(queryParams);
    const { data: recentOrdersData, isLoading: recentOrdersLoading } =
        useDashboardOrdersRecentOrders(queryParams);
    const { data: leadSourcesData, isLoading: leadSourcesLoading } =
        useDashboardOrdersLeadSources(queryParams);

    // Combinar datos para compatibilidad con componentes existentes
    const data = {
        // Summary data (más importante, se carga primero)
        stats: summaryData?.stats,
        conversionFunnel: summaryData?.conversionFunnel || [],
        weeklyStageEvolution: summaryData?.weeklyStageEvolution || [],
        filterOptions: summaryData?.filterOptions,

        // Agents data
        revenueBySalesAgent: agentsData?.revenueBySalesAgent || [],

        // Analytics data
        conversionBySaleStages: analyticsData?.conversionBySaleStages,
        currentMonthPerformance: analyticsData?.currentMonthPerformance,

        ordersByLeadSources: leadSourcesData,

        // Historical data
        ordersByMonth: historicalData?.ordersByMonth || [],
        // revenueByMonth: historicalData?.revenueByMonth || [],

        // Products data
        topSellingProducts: productsData?.topSellingProducts || [],

        // Recent orders data
        recentOrders: recentOrdersData?.recentOrders || [],

        // Datos derivados para compatibilidad
        ordersByStage: summaryData?.ordersByStage || [],
    };

    // Colores para los gráficos
    const stageColors = ["#4096ff", "#36cfc9", "#faad14", "#73d13d", "#ff4d4f"];
    const funnelColors = ["#4096ff", "#36cfc9", "#faad14", "#73d13d"];

    // Helper function to get tendency color
    const getTendencyColor = (tendency: "up" | "down" | "flat") => {
        switch (tendency) {
            case "up":
                return "text-green-500";
            case "down":
                return "text-red-500";
            case "flat":
                return "text-gray-500";
            default:
                return "text-gray-500";
        }
    };

    // Helper function to get tendency icon
    const getTendencyIcon = (tendency: DashboardOrderTendency) => {
        switch (tendency) {
            case "up":
                return "+";
            case "down":
                return "";
            case "flat":
                return "";
            default:
                return "";
        }
    };

    return (
        <div>
            {/* Filtros */}
            <OrderFilterCard filterOptions={data?.filterOptions} />
            {/* Stats Cards */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} sm={12} lg={8} xl={6}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    ) : (
                        <StatCard
                            title="Total en ventas"
                            value={`S/. ${data?.stats?.totalSalesAmount?.total?.toLocaleString() || "0.00"}`}
                            icon={<DollarSign size={24} className="text-green-500" />}
                            color="#73d13d"
                            info={
                                <div className="space-y-3">
                                    <div className="text-xs text-gray-600 italic">
                                        Total estimado de ventas por órdenes vendidas en
                                        el período seleccionado
                                    </div>

                                    <div className="grid grid-cols-2 gap-3">
                                        <div className="bg-gray-50 p-2 rounded">
                                            <div className="text-xs text-gray-500 mb-1">
                                                Soles (PEN)
                                            </div>
                                            <div className="text-sm font-semibold text-gray-800">
                                                S/.{" "}
                                                {data?.stats?.totalSalesAmount?.pen?.toLocaleString() ||
                                                    "0.00"}
                                            </div>
                                        </div>
                                        <div className="bg-gray-50 p-2 rounded">
                                            <div className="text-xs text-gray-500 mb-1">
                                                Dólares (USD)
                                            </div>
                                            <div className="text-sm font-semibold text-gray-800">
                                                ${" "}
                                                {data?.stats?.totalSalesAmount?.usd?.toLocaleString() ||
                                                    "0.00"}
                                            </div>
                                        </div>
                                    </div>

                                    {data?.stats?.totalSalesAmount
                                        ?.currencyExchangeRate && (
                                        <div className="pt-2 border-t border-gray-200">
                                            <div className="text-xs text-gray-600">
                                                <span className="font-medium">
                                                    Tasa de conversión:
                                                </span>{" "}
                                                1 USD ={" S/ "}
                                                {
                                                    data?.stats?.totalSalesAmount
                                                        ?.currencyExchangeRate
                                                }
                                            </div>
                                        </div>
                                    )}
                                </div>
                            }
                        />
                    )}
                </Col>
                <Col xs={24} sm={12} lg={8} xl={6}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    ) : (
                        <StatCard
                            title="Clientes"
                            value={`${data?.stats?.clients || "0"}`}
                            icon={<Users size={24} className="text-blue-500" />}
                            color="#4096ff"
                            info={
                                <div className="text-xs text-gray-600">
                                    Cantidad de clientes <strong>únicos</strong> que
                                    generaron órdenes en el periodo seleccionado
                                </div>
                            }
                        />
                    )}
                </Col>
                <Col xs={24} sm={12} lg={8} xl={6}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    ) : (
                        <StatCard
                            title="Conversión de prospectos"
                            value={`${data?.stats?.conversionRate?.value || 0}%`}
                            icon={<Percent size={24} className="text-purple-500" />}
                            color="#722ed1"
                            suffix={
                                data?.stats?.conversionRate && (
                                    <span
                                        className={`text-xs ${getTendencyColor(data.stats.conversionRate.tendency)}`}
                                    >
                                        {getTendencyIcon(
                                            data.stats.conversionRate.tendency,
                                        )}
                                        {data.stats.conversionRate.percentage !== 0
                                            ? `${data.stats.conversionRate.percentage}%`
                                            : "sin cambio"}
                                    </span>
                                )
                            }
                            info={
                                <div className="text-xs text-gray-600">
                                    <div className="font-medium mb-2">
                                        Tasa de conversión de prospectos generados en el
                                        período
                                    </div>
                                    <div className="mb-2">
                                        <strong>Cálculo:</strong> Órdenes vendidas ÷
                                        Solo prospectos creados en el período
                                    </div>
                                    <div className="mb-2">
                                        <strong>Propósito:</strong> Evaluar qué tan
                                        rápido los nuevos prospectos se convierten en
                                        ventas
                                    </div>
                                    <div className="italic text-gray-500">
                                        Mide la efectividad del equipo para cerrar
                                        ventas rápidamente
                                    </div>
                                </div>
                            }
                        />
                    )}
                </Col>
                <Col xs={24} sm={12} lg={8} xl={6}>
                    {summaryLoading ? (
                        <Card>
                            <Skeleton active paragraph={{ rows: 2 }} />
                        </Card>
                    ) : (
                        <StatCard
                            title="Ventas"
                            value={`${data?.stats?.salesThisMonth?.value || 0}`}
                            icon={<TrendingUp size={24} className="text-orange-500" />}
                            color="#fa8c16"
                            suffix={
                                data?.stats?.salesThisMonth && (
                                    <span
                                        className={`text-xs ${getTendencyColor(data.stats.salesThisMonth.tendency)}`}
                                    >
                                        {getTendencyIcon(
                                            data.stats.salesThisMonth.tendency,
                                        )}
                                        {data.stats.salesThisMonth.percentage !== 0
                                            ? `${data.stats.salesThisMonth.percentage}%`
                                            : "sin cambio"}
                                    </span>
                                )
                            }
                            info={
                                <div className="text-xs text-gray-600">
                                    <p>
                                        Cantidad de órdenes que han alcanzado el estado{" "}
                                        <strong>Vendido</strong> en el período
                                        seleccionado
                                    </p>
                                </div>
                            }
                        />
                    )}
                </Col>
            </Row>

            {/* Conversion Funnel */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={14}>
                    {summaryLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 8 }} />
                        </Card>
                    ) : (
                        <FunnelChartCard
                            title="Embudo de Conversión"
                            data={data?.conversionFunnel || []}
                            colors={funnelColors}
                            icon={<FilterX className="h-5 w-5 text-blue-500" />}
                            showConversionRates={true}
                        />
                    )}
                </Col>
                <Col xs={24} lg={10}>
                    {summaryLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 6 }} />
                        </Card>
                    ) : (
                        <>
                            <PieChartCard
                                title="Estado de las órdenes"
                                data={data?.ordersByStage || []}
                                colors={stageColors}
                                icon={<Activity className="h-5 w-5 text-yellow-500" />}
                            />
                        </>
                    )}
                </Col>
            </Row>

            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12} className="space-y-4">
                    {analyticsLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 8 }} />
                        </Card>
                    ) : (
                        <Card
                            title={
                                <div className="flex items-center">
                                    <LineChart className="mr-2 h-5 w-5 text-green-500" />
                                    <span>Conversión entre etapas</span>
                                </div>
                            }
                        >
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Card className="bg-gray-50">
                                    <Statistic
                                        title="Prospecto → Interesado"
                                        value={
                                            data?.conversionBySaleStages
                                                ?.prospectToInterested?.percentage || 0
                                        }
                                        suffix="%"
                                        precision={1}
                                        valueStyle={{ color: "#3f8600" }}
                                    />
                                    <div className="text-xs text-gray-500 mt-2">
                                        {data?.conversionBySaleStages
                                            ?.prospectToInterested?.toCount || 0}{" "}
                                        de{" "}
                                        {data?.conversionBySaleStages
                                            ?.prospectToInterested?.fromCount || 0}{" "}
                                        prospectos
                                    </div>
                                </Card>
                                <Card className="bg-gray-50">
                                    <Statistic
                                        title="Interesado → Por Pagar"
                                        value={
                                            data?.conversionBySaleStages
                                                ?.interestedToPay?.percentage || 0
                                        }
                                        suffix="%"
                                        precision={1}
                                        valueStyle={{ color: "#3f8600" }}
                                    />
                                    <div className="text-xs text-gray-500 mt-2">
                                        {data?.conversionBySaleStages?.interestedToPay
                                            ?.toCount || 0}{" "}
                                        de{" "}
                                        {data?.conversionBySaleStages?.interestedToPay
                                            ?.fromCount || 0}{" "}
                                        interesados
                                    </div>
                                </Card>
                                <Card className="bg-gray-50">
                                    <Statistic
                                        title="Por Pagar → Pagado"
                                        value={
                                            data?.conversionBySaleStages?.toPayToPaid
                                                ?.percentage || 0
                                        }
                                        suffix="%"
                                        precision={1}
                                        valueStyle={{ color: "#3f8600" }}
                                    />
                                    <div className="text-xs text-gray-500 mt-2">
                                        {data?.conversionBySaleStages?.toPayToPaid
                                            ?.toCount || 0}{" "}
                                        de{" "}
                                        {data?.conversionBySaleStages?.toPayToPaid
                                            ?.fromCount || 0}{" "}
                                        por pagar
                                    </div>
                                </Card>
                            </div>
                            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                                <div className="font-medium text-blue-800 mb-2">
                                    Resumen
                                </div>
                                <div className="flex justify-between">
                                    <div className="text-blue-700">
                                        <div className="text-sm">
                                            Tasa de conversión general
                                        </div>
                                        <div className="text-xl font-bold">
                                            {data?.conversionBySaleStages?.resume
                                                .generalConversionRate || "0.00"}
                                            %
                                        </div>
                                    </div>
                                    <div className="text-blue-700">
                                        <div className="text-sm">Pérdidas</div>
                                        <div className="text-xl font-bold">
                                            {data?.conversionBySaleStages?.resume
                                                .lost || "0"}{" "}
                                            órdenes
                                        </div>
                                    </div>
                                    <div className="text-green-700">
                                        <div className="text-sm">Ventas cerradas</div>
                                        <div className="text-xl font-bold">
                                            {data?.conversionBySaleStages?.resume
                                                .closedSales || "0"}{" "}
                                            órdenes
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    )}
                    {/* <Col xs={24} lg={12}> */}
                    {analyticsLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 6 }} />
                        </Card>
                    ) : (
                        <SalesPerformanceCard
                            title="Rendimiento Mensual"
                            data={data?.currentMonthPerformance}
                            icon={<TrendingUp className="h-5 w-5 text-blue-500" />}
                        />
                    )}
                    {/* </Col> */}
                </Col>
                <Col xs={24} lg={12}>
                    {leadSourcesLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 8 }} />
                        </Card>
                    ) : (
                        <LeadSourcesAnalyticsCard
                            title="Análisis por Fuentes de Lead"
                            data={data?.ordersByLeadSources}
                            icon={<BarChart2 className="h-5 w-5 text-blue-500" />}
                        />
                    )}
                </Col>
            </Row>

            {/* Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    {historicalLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 6 }} />
                        </Card>
                    ) : (
                        <BarChartCard
                            title="Órdenes por mes"
                            data={data?.ordersByMonth || []}
                            xAxisDataKey="month"
                            dataKey="total"
                            barColor="#4096ff"
                            icon={<BarChart2 className="h-5 w-5 text-blue-500" />}
                            stacked
                            stackedDataKeys={[
                                "prospect",
                                "interested",
                                "toPay",
                                "sold",
                                "lost",
                            ]}
                            legend={[
                                { name: "Prospecto", color: "#4096ff" },
                                { name: "Interesado", color: "#36cfc9" },
                                { name: "Por pagar", color: "#faad14" },
                                { name: "Vendido", color: "#73d13d" },
                                { name: "Perdido", color: "#ff4d4f" },
                            ]}
                        />
                    )}
                </Col>
                <Col xs={24} lg={12}>
                    {summaryLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 6 }} />
                        </Card>
                    ) : (
                        <LineChartCard
                            title="Evolución semanal por etapas"
                            data={data?.weeklyStageEvolution || []}
                            xAxisDataKey="day"
                            lineDataKeys={[
                                "prospect",
                                "interested",
                                "toPay",
                                "sold",
                                "lost",
                            ]}
                            lineColors={[
                                "#4096ff",
                                "#36cfc9",
                                "#faad14",
                                "#73d13d",
                                "#ff4d4f",
                            ]}
                            icon={<LineChart className="h-5 w-5 text-blue-500" />}
                            legend={[
                                { name: "Prospecto", color: "#4096ff" },
                                { name: "Interesado", color: "#36cfc9" },
                                { name: "Por pagar", color: "#faad14" },
                                { name: "Vendido", color: "#73d13d" },
                                { name: "Perdido", color: "#ff4d4f" },
                            ]}
                            height={300}
                            showLegend={true}
                            strokeWidth={2}
                        />
                    )}
                </Col>
                {/* <Col xs={24} lg={8}>
                    {summaryLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 6 }} />
                        </Card>
                    ) : (
                        <>
                            <PieChartCard
                                title="Estado de las órdenes"
                                data={data?.ordersByStage || []}
                                colors={stageColors}
                                icon={<Activity className="h-5 w-5 text-yellow-500" />}
                            />
                        </>
                    )}
                </Col> */}
            </Row>

            {/* Detailed Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={24}>
                    {productsLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 6 }} />
                        </Card>
                    ) : (
                        <TopProductsCard
                            title="Top Productos"
                            data={data?.topSellingProducts || []}
                            icon={<PieChart className="h-5 w-5 text-blue-500" />}
                            className="max-h-[500px] overflow-y-auto"
                        />
                    )}
                </Col>
            </Row>

            {/* Revenue by Sales Agent */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24}>
                    {agentsLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 4 }} />
                        </Card>
                    ) : (
                        <Card
                            title={
                                <div className="flex items-center">
                                    <Users className="mr-2 h-5 w-5 text-purple-500" />
                                    <span>Ventas por agente comercial</span>
                                </div>
                            }
                            className="h-full"
                        >
                            <Row gutter={[16, 16]}>
                                {data?.revenueBySalesAgent?.map((agent, index) => (
                                    <Col
                                        xs={24}
                                        sm={12}
                                        md={8}
                                        lg={6}
                                        key={agent.agentUid || index}
                                    >
                                        <Card className="bg-gray-50">
                                            <Statistic
                                                title={agent.name}
                                                value={agent.value}
                                                precision={0}
                                                valueStyle={{ color: "#3f8600" }}
                                                prefix="S/. "
                                                formatter={(value) =>
                                                    `${value.toLocaleString()}`
                                                }
                                            />
                                            <div className="text-xs text-gray-500 mt-2">
                                                {agent.percentage}% del total
                                            </div>
                                            {/* Barra de progreso de recaudación real usando Progress de antd */}
                                            <div className="mt-2">
                                                <div className="flex justify-between text-xs mb-1">
                                                    <span className="text-gray-600">
                                                        Recaudado
                                                    </span>
                                                    <span className="text-gray-600 font-semibold">
                                                        S/.{" "}
                                                        {agent.effectiveRevenue?.toLocaleString?.() ??
                                                            0}{" "}
                                                        de S/.{" "}
                                                        {agent.value?.toLocaleString?.() ??
                                                            0}
                                                    </span>
                                                </div>
                                                <Progress
                                                    percent={
                                                        agent.value
                                                            ? Math.min(
                                                                  100,
                                                                  (agent.effectiveRevenue /
                                                                      agent.value) *
                                                                      100,
                                                              )
                                                            : 0
                                                    }
                                                    showInfo={true}
                                                    size="small"
                                                    strokeColor="#3f8600"
                                                    trailColor="#e5e7eb"
                                                    format={(percent) =>
                                                        `${percent?.toFixed(1)}%`
                                                    }
                                                />
                                            </div>
                                        </Card>
                                    </Col>
                                ))}
                            </Row>
                        </Card>
                    )}
                </Col>
            </Row>

            {/* Recent Orders Table */}
            <Row gutter={[16, 16]}>
                <Col xs={24}>
                    {recentOrdersLoading ? (
                        <Card title={<Skeleton.Input style={{ width: 200 }} />}>
                            <Skeleton active paragraph={{ rows: 6 }} />
                        </Card>
                    ) : (
                        <Card
                            title={
                                <div className="flex items-center">
                                    <Activity className="mr-2 h-5 w-5 text-blue-500" />
                                    <span>Órdenes recientes</span>
                                </div>
                            }
                            className="mb-6"
                        >
                            <OrdersTable
                                orders={data?.recentOrders || []}
                                tableProps={{
                                    footer: () => (
                                        <div className="w-full flex justify-end">
                                            <Button
                                                onClick={() => navigate("/crm/orders")}
                                            >
                                                Ver más órdenes
                                            </Button>
                                        </div>
                                    ),
                                }}
                            />
                        </Card>
                    )}
                </Col>
            </Row>
        </div>
    );
}
