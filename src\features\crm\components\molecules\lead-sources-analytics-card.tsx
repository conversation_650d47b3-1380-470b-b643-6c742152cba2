import React, { useState } from "react";
import { Card, Switch, Empty, Progress } from "antd";
import { BarChart3 } from "lucide-react";
import type {
    DashboardOrderLeadSourcesData,
    OrdersByLeadSourceItem,
} from "../../types/dashboard/orders";

interface LeadSourcesAnalyticsCardProps {
    title?: string;
    data?: DashboardOrderLeadSourcesData;
    icon?: React.ReactNode;
}

const STAGE_COLORS = {
    prospect: "#4096ff",
    interested: "#36cfc9",
    toPay: "#faad14",
    sold: "#73d13d",
    lost: "#ff4d4f",
};

const STAGE_LABELS = {
    prospect: "Prospecto",
    interested: "Interesado",
    toPay: "Por pagar",
    sold: "Vendido",
    lost: "Perdido",
};

const LeadSourcesAnalyticsCard: React.FC<LeadSourcesAnalyticsCardProps> = ({
    title = "Análisis por Fuentes de Lead",
    data,
    icon = <BarChart3 className="h-5 w-5 text-blue-500" />,
}) => {
    const { ordersByLeadSources, crossLeadSourcesAnalysis } = data || {};

    const [viewMode, setViewMode] = useState<"leadOrigin" | "contactChannel">(
        "leadOrigin",
    );

    const currentData = ordersByLeadSources?.[viewMode] || [];

    // Calcular el total máximo para normalizar las barras
    const maxTotal = Math.max(...currentData.map((item) => item.ordersCount));

    const renderStageBar = (
        item: OrdersByLeadSourceItem,
        stage: keyof typeof STAGE_COLORS,
        count: number,
    ) => {
        if (count === 0) return null;

        const realWidth = item.ordersCount > 0 ? (count / maxTotal) * 100 : 0;

        return (
            <div className="flex items-center gap-2 w-full">
                <div className="text-xs text-gray-600 flex flex-col min-w-[100px]">
                    <span>{STAGE_LABELS[stage]}</span>
                </div>
                <Progress
                    percent={realWidth}
                    showInfo={false}
                    size="small"
                    strokeColor={STAGE_COLORS[stage]}
                    trailColor="#e5e7eb"
                />
                <div className="text-xs text-gray-600 flex flex-col min-w-[100px]">
                    <span>{count} órdenes</span>
                </div>
            </div>
        );
    };

    const renderLegend = () => (
        <div className="flex flex-wrap gap-4 mb-4">
            {Object.entries(STAGE_LABELS).map(([key, label]) => (
                <div key={key} className="flex items-center gap-2">
                    <div
                        className="w-3 h-3 rounded-full"
                        style={{
                            backgroundColor:
                                STAGE_COLORS[key as keyof typeof STAGE_COLORS],
                        }}
                    />
                    <span className="text-sm text-gray-600">{label}</span>
                </div>
            ))}
        </div>
    );

    return (
        <Card
            title={
                <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                        {icon}
                        <span>{title}</span>
                    </div>
                </div>
            }
            className="shadow-md h-full"
        >
            {/* Gráfico de barras de cross */}
            {/* <ContactChannelBarChart 
                data={crossLeadSourcesAnalysis}
            /> */}

            {/* Desglose por origen o canal de leads */}
            <div>
                <h3>Desglose</h3>
                <div className="flex items-center gap-2">
                    <Switch
                        unCheckedChildren="Origen de Lead"
                        checkedChildren="Canal de Contacto"
                        checked={viewMode === "contactChannel"}
                        onChange={(checked) =>
                            setViewMode(checked ? "contactChannel" : "leadOrigin")
                        }
                        size="small"
                    />
                </div>
                {currentData.length > 0 ? (
                    <div>
                        {renderLegend()}
                        <div className="space-y-4">
                            {currentData.map((item, index) => (
                                <div
                                    key={index}
                                    className="border-b border-gray-100 pb-4 last:border-b-0"
                                >
                                    <div className="flex justify-between items-center mb-2">
                                        <h4 className="font-medium text-gray-800">
                                            {item.name}
                                        </h4>
                                        <div className="text-right divide-x-2 space-x-2">
                                            <span className="font-semibold text-gray-600">
                                                {item.ordersCount.toLocaleString()}{" "}
                                                órdenes
                                            </span>
                                            <span className="text-sm text-gray-500 pl-2">
                                                {item.percentage}% del total
                                            </span>
                                        </div>
                                    </div>

                                    <div className="flex flex-wrap items-start gap-1">
                                        {renderStageBar(
                                            item,
                                            "prospect",
                                            item.prospectCount,
                                        )}
                                        {renderStageBar(
                                            item,
                                            "interested",
                                            item.interestedCount,
                                        )}
                                        {renderStageBar(item, "toPay", item.toPayCount)}
                                        {renderStageBar(item, "sold", item.soldCount)}
                                        {renderStageBar(item, "lost", item.lostCount)}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                ) : (
                    <div className="h-64 flex items-center justify-center">
                        <Empty description="No hay datos disponibles." />
                    </div>
                )}
            </div>
        </Card>
    );
};

export default LeadSourcesAnalyticsCard;
