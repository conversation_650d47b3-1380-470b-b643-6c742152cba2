import {
    Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>Axis,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Legend,
    CartesianGrid,
} from "recharts";
import type { CrossLeadSourceAnalysisItem } from "../../types/dashboard/orders";
import { OrderStageLabels } from "../../types/order";
import { Empty } from "antd";

interface ContactChannelBarChartProps {
    labels: Record<string, string>;
    colors: Record<string, string>;
    data?: CrossLeadSourceAnalysisItem[];
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        color: string;
        name: string;
        value: string | number;
        dataKey: string;
    }>;
    label?: string;
}

// Componente del gráfico de barras mixto
const ContactChannelBarChart: React.FC<ContactChannelBarChartProps> = ({
    labels,
    colors,
    data = [],
}) => {
    // Transformar los datos para el gráfico
    const chartData = data.map((channel) => {
        const baseData: Record<string, string | number> = {
            contactChannel: channel.contactC<PERSON>nel || "",
            totalOrders: channel.totalOrders || 0,
            totalProspect: channel.totalProspectCount || 0,
            totalInterested: channel.totalInterestedCount || 0,
            totalToPay: channel.totalToPayCount || 0,
            totalSold: channel.totalSoldCount || 0,
            totalLost: channel.totalLostCount || 0,
        };

        // Agregar datos de lead origins como propiedades adicionales
        const leadOrigins = channel.leadOrigins || [];
        leadOrigins.forEach((origin, originIndex) => {
            const prefix = `origin${originIndex + 1}`;
            baseData[`${prefix}Name`] = origin.name;
            baseData[`${prefix}Orders`] = origin.ordersCount || 0;
            baseData[`${prefix}Prospect`] = origin.prospectCount || 0;
            baseData[`${prefix}Interested`] = origin.interestedCount || 0;
            baseData[`${prefix}ToPay`] = origin.toPayCount || 0;
            baseData[`${prefix}Sold`] = origin.soldCount || 0;
            baseData[`${prefix}Lost`] = origin.lostCount || 0;
        });

        return baseData;
    });

    const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const channelData = data.find((item) => item.contactChannel === label);
            if (!channelData) return null;

            const totalOrders = channelData.totalOrders || 0;
            const totalPercentage = channelData.percentageOfTotal || 0;

            return (
                <div className="bg-white-full p-3 border border-gray-200 rounded-lg shadow-lg min-w-[250px] z-50">
                    <p className="font-semibold text-gray-800 mb-2 text-sm">
                        {`Canal: ${label}`}
                    </p>
                    <div className="mb-3 pb-2 border-b border-gray-100">
                        <p className="text-xs text-gray-600">
                            <span className="font-medium">Total:</span>{" "}
                            <span className="font-semibold text-gray-800">
                                {totalOrders.toLocaleString()} órdenes
                            </span>
                        </p>
                        <p className="text-xs text-gray-600">
                            <span className="font-medium">Porcentaje del total:</span>{" "}
                            <span className="font-semibold text-gray-800">
                                {totalPercentage}%
                            </span>
                        </p>
                    </div>
                    <div className="space-y-1">
                        {payload.map((entry, index) => {
                            const stageKey = entry.dataKey
                                .replace("total", "")
                                .toLowerCase();
                            const stageLabel =
                                OrderStageLabels[
                                    stageKey as keyof typeof OrderStageLabels
                                ];

                            if (!stageLabel) return null;

                            return (
                                <p key={index} className="text-xs">
                                    <span
                                        className="inline-block w-3 h-3 rounded-full mr-2"
                                        style={{ backgroundColor: entry.color }}
                                    />
                                    <span className="font-medium">{stageLabel}:</span>{" "}
                                    <span className="font-semibold text-gray-800">
                                        {entry.value.toLocaleString()}
                                    </span>
                                </p>
                            );
                        })}
                    </div>

                    {/* Mostrar lead origins */}
                    {channelData.leadOrigins && channelData.leadOrigins.length > 0 && (
                        <div className="mt-3 pt-2 border-t border-gray-100">
                            <p className="text-xs font-medium text-gray-700 mb-1">
                                Orígenes de Lead:
                            </p>
                            {channelData.leadOrigins.map((origin, index) => (
                                <p key={index} className="text-xs text-gray-600">
                                    • {origin.name}: {origin.ordersCount} órdenes (
                                    {origin.percentage || 0}%)
                                </p>
                            ))}
                        </div>
                    )}
                </div>
            );
        }
        return null;
    };

    const getStageLabel = (dataKey: string) => {
        const stageKey = dataKey.replace("total", "").toLowerCase();
        return labels[stageKey as keyof typeof labels] || dataKey;
    };

    if (!data.length) {
        return (
            <div className="h-80 flex items-center justify-center">
                <Empty description="No hay datos disponibles para el gráfico" />
            </div>
        );
    }

    console.log(chartData);

    return (
        <div className="h-80 mb-6">
            <ResponsiveContainer width="100%" height="100%">
                <BarChart
                    data={chartData}
                    margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 60,
                    }}
                    maxBarSize={80}
                >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                        dataKey="contactChannel"
                        tick={{ fontSize: 12 }}
                        stroke="#666"
                        angle={-45}
                        textAnchor="end"
                        height={80}
                    />
                    <YAxis tick={{ fontSize: 12 }} stroke="#666" />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend
                        formatter={(value) => getStageLabel(value)}
                        wrapperStyle={{ paddingTop: "20px" }}
                    />

                    {/* Barras apiladas verticalmente por estado */}
                    <Bar
                        dataKey="totalProspect"
                        stackId="stages"
                        fill={colors.prospect}
                        name="totalProspect"
                        radius={[0, 0, 0, 0]}
                    />
                    <Bar
                        dataKey="totalInterested"
                        stackId="stages"
                        fill={colors.interested}
                        name="totalInterested"
                        radius={[0, 0, 0, 0]}
                    />
                    <Bar
                        dataKey="totalToPay"
                        stackId="stages"
                        fill={colors.toPay}
                        name="totalToPay"
                        radius={[0, 0, 0, 0]}
                    />
                    <Bar
                        dataKey="totalSold"
                        stackId="stages"
                        fill={colors.sold}
                        name="totalSold"
                        radius={[0, 0, 0, 0]}
                    />
                    <Bar
                        dataKey="totalLost"
                        stackId="stages"
                        fill={colors.lost}
                        name="totalLost"
                        radius={[2, 2, 0, 0]}
                    />
                </BarChart>
            </ResponsiveContainer>
        </div>
    );
};

export default ContactChannelBarChart;
